# Discord Self-Bot with NestJS

⚠️ **WARNING: This project is for educational purposes only!**

Self-bots violate Discord's Terms of Service and can result in account termination. Use this code only for learning purposes and testing in private servers where you have permission.

## Features

- 🏗️ Built with NestJS framework
- 🔧 TypeScript support
- 🌍 Environment-based configuration
- 📝 Comprehensive logging

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd discord-selfbot-nestjs
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   ```bash
   npm run setup
   ```

4. **Configure your Discord token:**
   ```bash
   # Edit the .env file and add your Discord user token
   nano .env
   # or use any text editor to edit .env
   ```

5. **Verify your token setup:**
   ```bash
   npm run verify-token
   ```

## Getting Your Discord Token

⚠️ **IMPORTANT:** Never share your Discord token with anyone!

### Method 1: Browser Developer Tools
1. Open Discord in your web browser
2. Press F12 to open Developer Tools
3. Go to the Network tab
4. Send a message in any channel
5. Look for a request to `https://discord.com/api/v*/messages`
6. In the request headers, find `authorization` - this is your token

### Method 2: Console Method
1. Open Discord in your web browser
2. Press F12 to open Developer Tools
3. Go to the Console tab
4. Paste this code and press Enter:
   ```javascript
   (webpackChunkdiscord_app.push([[''],{},e=>{m=[];for(let c in e.c)m.push(e.c[c])}]),m).find(m=>m?.exports?.default?.getToken!==void 0).exports.default.getToken()
   ```
5. Copy the returned token (without quotes)

⚠️ **Security Warning:** Be extremely careful with your token. Anyone with access to it can control your Discord account!

## Usage

### Development Mode
```bash
npm run start:dev
```

### Production Mode
```bash
npm run build
npm run start:prod
```

## Scripts

- `npm run build` - Build the application
- `npm run start` - Start the application
- `npm run start:dev` - Start in development mode with hot reload
- `npm run start:debug` - Start in debug mode
- `npm run start:prod` - Start in production mode
- `npm run lint` - Run ESLint
- `npm run test` - Run tests

## Legal Disclaimer

This project is created for educational purposes only. The use of self-bots violates Discord's Terms of Service and may result in account suspension or termination. The developers of this project are not responsible for any consequences resulting from the use of this code.

**Use at your own risk and only for learning purposes!**

## License

- MIT License - see LICENSE file for details.
- Author: NirussVn0
