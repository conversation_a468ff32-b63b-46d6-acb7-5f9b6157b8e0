{"name": "discord-selfbot-nestjs", "version": "1.0.0", "description": "A Discord self-bot built with NestJS for educational purposes", "main": "dist/main.js", "scripts": {"build": "tsc", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect-brk src/main.ts", "start:prod": "node dist/main.js", "verify-token": "node scripts/token-wrapper.js verify-env", "check-token": "node scripts/token-wrapper.js check-api", "test-token-direct": "node scripts/token-wrapper.js test-direct", "setup": "cp .env.example .env && echo 'Please edit .env file and add your Discord token'", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": ["discord", "selfbot", "<PERSON><PERSON><PERSON>", "typescript", "educational"], "author": "", "license": "MIT", "devDependencies": {"@nestjs/testing": "^11.1.3", "@types/jest": "^29.5.14", "jest": "^30.0.0", "ts-jest": "^29.4.0"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/platform-express": "^11.1.3", "discord.js-selfbot-v13": "^3.6.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}, "optionalDependencies": {"discord-token-checker": "file:./discord-token-checker"}}