# Discord Self-Bot Architecture Documentation

## 🏗️ Project Architecture Overview

This Discord self-bot is built using **Clean Architecture** principles with **NestJS** framework, implementing **SOLID** design patterns and **Object-Oriented Programming** best practices.

### 📊 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Discord UI    │  │   Console Logs  │  │  HTTP API   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Discord Service │  │ Command Registry│  │ Bot Stats   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Commands      │  │   Interfaces    │  │    Types    │ │
│  │  (Ping, etc.)   │  │   (ICommand)    │  │ (Results)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Discord.js API  │  │  Configuration  │  │   Logging   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🏛️ SOLID Principles Implementation

### 1. **Single Responsibility Principle (SRP)**
- **DiscordService**: Manages Discord client connection and message routing
- **CommandRegistryService**: Handles command registration and retrieval
- **BotStatsService**: Tracks bot statistics and health
- **PingCommand**: Implements only ping functionality

### 2. **Open/Closed Principle (OCP)**
- **BaseCommand**: Abstract class open for extension, closed for modification
- **ICommand**: Interface allows new commands without changing existing code
- **CommandRegistry**: Can accept new commands without modification

### 3. **Liskov Substitution Principle (LSP)**
- All command implementations can replace **BaseCommand**
- **ICommand** implementations are interchangeable

### 4. **Interface Segregation Principle (ISP)**
- **ICommand**: Focused interface for command behavior
- **ICommandRegistry**: Specific interface for command management
- **BotStats**: Dedicated interface for statistics

### 5. **Dependency Inversion Principle (DIP)**
- High-level modules depend on abstractions (**ICommand**)
- Low-level modules implement interfaces
- Dependency injection throughout the application

## 📁 Directory Structure

```
src/
├── config/                     # Configuration Layer
│   ├── config.module.ts        # Configuration module
│   └── config.service.ts       # Environment configuration
├── discord/                    # Discord Domain
│   ├── commands/               # Command Implementations
│   │   ├── base/              # Base Classes
│   │   │   └── base.command.ts # Abstract command base
│   │   ├── ping.command.ts    # Ping command implementation
│   │   └── ping.command.spec.ts # Unit tests
│   ├── interfaces/            # Domain Interfaces
│   │   └── command.interface.ts # Command contracts
│   ├── services/              # Domain Services
│   │   ├── command-registry.service.ts # Command management
│   │   └── bot-stats.service.ts # Statistics tracking
│   ├── types/                 # Type Definitions
│   │   └── command.types.ts   # Command-related types
│   ├── discord.module.ts      # Discord module configuration
│   └── discord.service.ts     # Main Discord service
├── app.module.ts              # Root application module
└── main.ts                    # Application entry point
```

## 🔧 Component Relationships

### Core Services Interaction

```mermaid
graph TD
    A[DiscordService] --> B[CommandRegistryService]
    A --> C[BotStatsService]
    A --> D[Discord Client]
    B --> E[ICommand Implementations]
    E --> F[PingCommand]
    F --> G[BaseCommand]
    C --> H[Statistics Data]
    A --> I[Message Handling]
    I --> B
    B --> E
```

### Command Execution Flow

```mermaid
sequenceDiagram
    participant U as User
    participant D as DiscordService
    participant R as CommandRegistry
    participant C as PingCommand
    participant S as BotStats
    
    U->>D: Send ".ping" message
    D->>R: Find matching command
    R->>D: Return PingCommand
    D->>C: Execute command
    C->>C: Get WebSocket latency
    C->>U: Edit message "pong 50ms"
    C->>D: Return execution result
    D->>S: Increment command counter
```

## 🎯 Design Patterns Used

### 1. **Command Pattern**
- **ICommand**: Command interface
- **BaseCommand**: Abstract command implementation
- **PingCommand**: Concrete command
- **CommandRegistry**: Command invoker

### 2. **Registry Pattern**
- **CommandRegistryService**: Centralized command storage
- Dynamic command registration and retrieval

### 3. **Factory Pattern**
- **Discord Module**: Factory for creating command instances
- Dependency injection as factory method

### 4. **Observer Pattern**
- **Discord Events**: Event-driven architecture
- **BotStatsService**: Observes Discord events

### 5. **Strategy Pattern**
- **Commands**: Different strategies for handling messages
- **BaseCommand**: Template method pattern

## 🔍 Class Diagrams

### Command Hierarchy

```
ICommand (Interface)
    ├── execute(message): Promise<CommandExecutionResult>
    ├── name: string
    ├── description: string
    └── trigger: string

BaseCommand (Abstract Class)
    ├── implements ICommand
    ├── logger: Logger
    ├── config: CommandConfig
    ├── execute(message): Promise<CommandExecutionResult>
    ├── executeCommand(message): Promise<CommandExecutionResult> [abstract]
    ├── handleError(message, error): Promise<void>
    └── validateMessage(message): boolean

PingCommand (Concrete Class)
    ├── extends BaseCommand
    ├── client: Client
    ├── executeCommand(message): Promise<CommandExecutionResult>
    └── constructor(client: Client)
```

### Service Dependencies

```
DiscordService
    ├── configService: AppConfigService
    ├── commandRegistry: CommandRegistryService
    ├── botStats: BotStatsService
    ├── client: Client
    └── methods: initializeBot(), handleMessage(), etc.

CommandRegistryService
    ├── commands: Map<string, ICommand>
    └── methods: register(), getCommand(), getAllCommands()

BotStatsService
    ├── client: Client
    ├── statistics: BotStats
    └── methods: getStats(), incrementCommands(), etc.
```

## 🚀 Key Features

### 1. **Enhanced Ping Command**
- Real WebSocket latency measurement
- Graceful error handling
- Response time tracking
- Metadata collection

### 2. **Command System**
- Extensible command architecture
- Automatic command registration
- Type-safe command execution
- Comprehensive error handling

### 3. **Statistics Tracking**
- Real-time bot statistics
- Health monitoring
- Performance metrics
- Activity tracking

### 4. **Clean Architecture**
- Separation of concerns
- Dependency injection
- Interface-based design
- Testable components

## 🧪 Testing Strategy

### Unit Tests
- **Command Testing**: Mock Discord client and messages
- **Service Testing**: Isolated service functionality
- **Integration Testing**: Component interaction

### Test Coverage
- Command execution paths
- Error handling scenarios
- Edge cases and validation
- Performance characteristics

## 📈 Scalability Considerations

### Adding New Commands
1. Create new command class extending **BaseCommand**
2. Implement **executeCommand** method
3. Register in **DiscordModule**
4. Add unit tests

### Performance Optimization
- Command caching in registry
- Lazy loading of commands
- Efficient message processing
- Memory usage monitoring

### Monitoring and Observability
- Structured logging with NestJS Logger
- Performance metrics collection
- Health check endpoints
- Error tracking and reporting

This architecture ensures maintainability, testability, and extensibility while following industry best practices for enterprise-grade applications.
