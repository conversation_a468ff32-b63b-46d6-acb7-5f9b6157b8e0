import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Client, Message } from 'discord.js-selfbot-v13';
import { AppConfigService } from '../config/config.service';
import { CommandRegistryService } from './services/command-registry.service';
import { BotStatsService } from './services/bot-stats.service';
import { ICommand } from './interfaces/command.interface';
import { ConnectionStatus } from './types/command.types';

/**
 * Main Discord service responsible for managing the Discord client
 * and coordinating between different services
 */
@Injectable()
export class DiscordService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(DiscordService.name);
  private client: Client;
  private botStats: BotStatsService;

  constructor(
    private readonly configService: AppConfigService,
    private readonly commandRegistry: CommandRegistryService,
  ) {
    this.client = new Client();
    // Initialize bot stats service with the client
    this.botStats = new BotStatsService(this.client);
  }

  /**
   * Initialize the Discord service when the module starts
   */
  async onModuleInit(): Promise<void> {
    await this.initializeBot();
  }

  /**
   * Cleanup when the module is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    await this.shutdown();
  }

  /**
   * Initialize the Discord bot with proper error handling
   */
  private async initializeBot(): Promise<void> {
    try {
      this.logger.log('🔄 Initializing Discord bot...');

      // Setup event handlers before connecting
      this.setupEventHandlers();

      // Get token from configuration
      const token = this.configService.discordToken;
      this.logger.log('🔑 Token loaded, attempting to connect...');

      // Connect to Discord
      await this.client.login(token);

      this.logger.log('✅ Successfully connected to Discord');
    } catch (error) {
      this.logger.error('❌ Failed to connect to Discord:', error.message);
      this.logger.error('❌ Full error:', error);

      // Provide specific guidance based on error type
      this.handleConnectionError(error);

      throw error;
    }
  }

  /**
   * Handle connection errors with specific guidance
   * @param error - The error that occurred during connection
   */
  private handleConnectionError(error: Error): void {
    if (error.message.includes('TOKEN_INVALID')) {
      this.logger.error('💡 Token is invalid. Please check:');
      this.logger.error('   1. Token is correctly copied from Discord');
      this.logger.error('   2. Token is a USER token, not a BOT token');
      this.logger.error('   3. Token hasn\'t expired');
      this.logger.error('   4. Run: npm run check-token to verify');
    } else if (error.message.includes('RATE_LIMITED')) {
      this.logger.error('💡 Rate limited. Please wait before retrying.');
    } else if (error.message.includes('NETWORK')) {
      this.logger.error('💡 Network error. Check your internet connection.');
    } else {
      this.logger.error('💡 Unknown error. Check Discord status and try again.');
    }
  }

  /**
   * Setup Discord client event handlers
   */
  private setupEventHandlers(): void {
    this.client.on('ready', () => {
      this.logger.log(`🤖 Logged in as ${this.client.user?.tag}`);
      this.logger.log(`🆔 User ID: ${this.client.user?.id}`);
      this.logger.warn('⚠️  Self-bot is now active - Use responsibly!');
    });

    this.client.on('messageCreate', async (message: Message) => {
      await this.handleMessage(message);
    });

    this.client.on('error', (error) => {
      this.logger.error('Discord client error:', error);
    });

    this.client.on('disconnect', () => {
      this.logger.warn('🔌 Disconnected from Discord');
    });

    this.client.on('debug', (info) => {
      if (this.configService.isDevelopment) {
        this.logger.debug(`Discord Debug: ${info}`);
      }
    });

    this.client.on('warn', (info) => {
      this.logger.warn(`Discord Warning: ${info}`);
    });
  }

  /**
   * Handle incoming Discord messages
   * @param message - The Discord message to process
   */
  private async handleMessage(message: Message): Promise<void> {
    try {
      // Only respond to messages from the bot user (self)
      if (message.author.id !== this.client.user?.id) {
        return;
      }

      const content = message.content.trim();

      // Find matching command
      const command = this.findMatchingCommand(content);
      if (command) {
        this.logger.debug(`Executing command: ${command.name}`);

        // Execute command and track statistics
        const result = await command.execute(message);
        this.botStats.incrementCommandsExecuted();

        if (result.success) {
          this.logger.debug(`Command ${command.name} executed successfully`);
        } else {
          this.logger.warn(`Command ${command.name} failed: ${result.error}`);
        }
      }
    } catch (error) {
      this.logger.error('Error handling message:', error);
    }
  }

  /**
   * Find a command that matches the message content
   * @param content - The message content
   * @returns The matching command or undefined
   */
  private findMatchingCommand(content: string): ICommand | undefined {
    const commands = this.commandRegistry.getAllCommands();

    for (const command of commands) {
      if (content.startsWith(command.trigger)) {
        return command;
      }
    }

    return undefined;
  }

  /**
   * Register a command with the registry
   * @param command - The command to register
   */
  registerCommand(command: ICommand): void {
    this.commandRegistry.register(command);
    this.logger.debug(`Registered command: ${command.name}`);
  }

  /**
   * Get the Discord client instance
   * @returns The Discord client
   */
  getClient(): Client {
    return this.client;
  }

  /**
   * Gracefully shutdown the Discord service
   */
  private async shutdown(): Promise<void> {
    if (this.client) {
      this.logger.log('🔌 Disconnecting from Discord...');
      await this.client.destroy();
      this.logger.log('✅ Discord service shutdown complete');
    }
  }
}
