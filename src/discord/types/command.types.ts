/**
 * Result of command execution
 */
export interface CommandExecutionResult {
  /**
   * Whether the command executed successfully
   */
  success: boolean;

  /**
   * The response message sent to Discord
   */
  response?: string;

  /**
   * Error message if execution failed
   */
  error?: string;

  /**
   * WebSocket latency in milliseconds (for ping command)
   */
  latency?: number;

  /**
   * Time taken to process the command in milliseconds
   */
  responseTime?: number;

  /**
   * Additional metadata about the execution
   */
  metadata?: Record<string, any>;
}

/**
 * Command configuration options
 */
export interface CommandConfig {
  /**
   * Whether the command is enabled
   */
  enabled: boolean;

  /**
   * Cooldown period in milliseconds
   */
  cooldown?: number;

  /**
   * Required permissions (for future use)
   */
  permissions?: string[];

  /**
   * Command aliases
   */
  aliases?: string[];
}

/**
 * Discord client connection status
 */
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

/**
 * Bot statistics
 */
export interface BotStats {
  /**
   * Connection status
   */
  status: ConnectionStatus;

  /**
   * Current WebSocket ping
   */
  ping: number;

  /**
   * Bot uptime in milliseconds
   */
  uptime: number;

  /**
   * Number of commands executed
   */
  commandsExecuted: number;

  /**
   * Number of messages processed
   */
  messagesProcessed: number;

  /**
   * Last activity timestamp
   */
  lastActivity: Date;
}
