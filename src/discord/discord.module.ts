import { Module } from '@nestjs/common';
import { DiscordService } from './discord.service';
import { PingCommand } from './commands/ping.command';
import { CommandRegistryService } from './services/command-registry.service';
import { AppConfigModule } from '../config/config.module';

/**
 * Discord module that provides all Discord-related services and commands
 */
@Module({
  imports: [AppConfigModule],
  providers: [
    // Core services
    DiscordService,
    CommandRegistryService,

    // Commands factory
    {
      provide: PingCommand,
      useFactory: (discordService: DiscordService) => {
        return new PingCommand(discordService.getClient());
      },
      inject: [DiscordService],
    },

    // Factory for registering commands
    {
      provide: 'COMMAND_REGISTRATION',
      useFactory: (
        discordService: DiscordService,
        pingCommand: PingCommand,
      ) => {
        // Register all commands
        discordService.registerCommand(pingCommand);
        return true;
      },
      inject: [DiscordService, PingCommand],
    },
  ],
  exports: [DiscordService, CommandRegistryService],
})
export class DiscordModule { }
