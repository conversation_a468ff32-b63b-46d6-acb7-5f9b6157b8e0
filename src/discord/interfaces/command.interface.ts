import { Message } from 'discord.js-selfbot-v13';
import { CommandExecutionResult } from '../types/command.types';

/**
 * Interface for Discord bot commands
 * Defines the contract that all commands must implement
 */
export interface ICommand {
  /**
   * The name of the command
   */
  readonly name: string;

  /**
   * Description of what the command does
   */
  readonly description: string;

  /**
   * The trigger text that activates this command
   */
  readonly trigger: string;

  /**
   * Execute the command
   * @param message - The Discord message that triggered the command
   * @returns Promise with execution result
   */
  execute(message: Message): Promise<CommandExecutionResult>;
}

/**
 * Interface for command registry
 * Manages registration and retrieval of commands
 */
export interface ICommandRegistry {
  /**
   * Register a command
   * @param command - The command to register
   */
  register(command: ICommand): void;

  /**
   * Get a command by its trigger
   * @param trigger - The trigger text
   * @returns The command if found, undefined otherwise
   */
  getCommand(trigger: string): ICommand | undefined;

  /**
   * Get all registered commands
   * @returns Array of all commands
   */
  getAllCommands(): ICommand[];

  /**
   * Check if a trigger is registered
   * @param trigger - The trigger text to check
   * @returns True if trigger is registered
   */
  hasCommand(trigger: string): boolean;
}
