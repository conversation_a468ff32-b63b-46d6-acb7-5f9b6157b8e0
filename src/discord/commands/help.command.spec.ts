import { Test, TestingModule } from '@nestjs/testing';
import { HelpCommand } from './help.command';
import { CommandRegistryService } from '../services/command-registry.service';
import { Message } from 'discord.js-selfbot-v13';
import { ICommand } from '../interfaces/command.interface';

describe('HelpCommand', () => {
  let command: HelpCommand;
  let mockCommandRegistry: any;
  let mockCommands: ICommand[];

  beforeEach(async () => {
    // Create mock commands
    mockCommands = [
      {
        name: 'ping',
        description: 'Responds with pong and WebSocket latency',
        trigger: '.ping',
        execute: jest.fn(),
      },
      {
        name: 'help',
        description: 'Shows available commands and their descriptions',
        trigger: '.help',
        execute: jest.fn(),
      },
      {
        name: 'status',
        description: 'Shows bot status information',
        trigger: '.status',
        execute: jest.fn(),
      },
    ];

    // Create mock command registry
    mockCommandRegistry = {
      getAllCommands: jest.fn().mockReturnValue(mockCommands),
      getCommand: jest.fn(),
      register: jest.fn(),
      hasCommand: jest.fn(),
      unregister: jest.fn(),
      clear: jest.fn(),
      getStats: jest.fn(),
      findCommands: jest.fn(),
      validateCommands: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: HelpCommand,
          useFactory: () => new HelpCommand(mockCommandRegistry),
        },
        {
          provide: CommandRegistryService,
          useValue: mockCommandRegistry,
        },
      ],
    }).compile();

    command = module.get<HelpCommand>(HelpCommand);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
  });

  it('should have correct properties', () => {
    expect(command.name).toBe('help');
    expect(command.description).toBe('Shows available commands and their descriptions');
    expect(command.trigger).toBe('.help');
  });

  it('should execute help command successfully', async () => {
    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(true);
    expect(result.response).toBeDefined();
    expect(result.metadata?.commandCount).toBe(3);
    expect(mockMessage.edit).toHaveBeenCalledWith(expect.stringContaining('📚 **Available Commands**'));
  });

  it('should include all commands in help response', async () => {
    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.response).toContain('.ping');
    expect(result.response).toContain('.help');
    expect(result.response).toContain('.status');
    expect(result.response).toContain('Responds with pong and WebSocket latency');
  });

  it('should handle empty command list', async () => {
    mockCommandRegistry.getAllCommands.mockReturnValue([]);

    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(true);
    expect(result.response).toBe('❌ No commands available');
    expect(mockMessage.edit).toHaveBeenCalledWith('❌ No commands available');
  });

  it('should sort commands alphabetically', async () => {
    // Add commands in non-alphabetical order
    const unsortedCommands = [
      {
        name: 'zebra',
        description: 'Z command',
        trigger: '.zebra',
        execute: jest.fn(),
      },
      {
        name: 'alpha',
        description: 'A command',
        trigger: '.alpha',
        execute: jest.fn(),
      },
      {
        name: 'beta',
        description: 'B command',
        trigger: '.beta',
        execute: jest.fn(),
      },
    ];

    mockCommandRegistry.getAllCommands.mockReturnValue(unsortedCommands);

    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    // Check that alpha comes before beta, which comes before zebra
    const response = result.response!;
    const alphaIndex = response.indexOf('.alpha');
    const betaIndex = response.indexOf('.beta');
    const zebraIndex = response.indexOf('.zebra');

    expect(alphaIndex).toBeLessThan(betaIndex);
    expect(betaIndex).toBeLessThan(zebraIndex);
  });

  it('should handle commands without descriptions', async () => {
    const commandsWithoutDesc = [
      {
        name: 'test',
        description: undefined,
        trigger: '.test',
        execute: jest.fn(),
      },
    ];

    mockCommandRegistry.getAllCommands.mockReturnValue(commandsWithoutDesc);

    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.response).toContain('No description available');
  });

  it('should handle invalid message format', async () => {
    const mockMessage = {
      content: 'invalid',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Invalid message format');
  });

  it('should handle registry errors gracefully', async () => {
    mockCommandRegistry.getAllCommands.mockImplementation(() => {
      throw new Error('Registry error');
    });

    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(false);
    expect(result.error).toContain('Failed to generate help');
  });

  it('should include proper formatting in help response', async () => {
    const mockMessage = {
      content: '.help',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    const response = result.response!;
    
    // Check for proper formatting elements
    expect(response).toContain('📚 **Available Commands**');
    expect(response).toContain('💡 **Usage**');
    expect(response).toContain('⚠️ **Note**');
    expect(response).toContain('Self-bot for educational purposes only');
  });

  it('should build detailed help for specific command', () => {
    const mockCommand = {
      name: 'ping',
      description: 'Test ping command',
      trigger: '.ping',
      execute: jest.fn(),
      getConfig: jest.fn().mockReturnValue({
        enabled: true,
        cooldown: 1000,
        aliases: ['p'],
      }),
    };

    mockCommandRegistry.getCommand.mockReturnValue(mockCommand);

    const detailedHelp = command.buildDetailedHelp('ping');

    expect(detailedHelp).toContain('📖 **Help for `.ping`**');
    expect(detailedHelp).toContain('Test ping command');
    expect(detailedHelp).toContain('Enabled: ✅');
    expect(detailedHelp).toContain('Cooldown: 1000ms');
    expect(detailedHelp).toContain('Aliases: p');
  });

  it('should return null for non-existent command in detailed help', () => {
    mockCommandRegistry.getCommand.mockReturnValue(undefined);

    const detailedHelp = command.buildDetailedHelp('nonexistent');

    expect(detailedHelp).toBeNull();
  });

  it('should handle commands without getConfig method', () => {
    const mockCommand = {
      name: 'simple',
      description: 'Simple command',
      trigger: '.simple',
      execute: jest.fn(),
      // No getConfig method
    };

    mockCommandRegistry.getCommand.mockReturnValue(mockCommand);

    const detailedHelp = command.buildDetailedHelp('simple');

    expect(detailedHelp).toContain('📖 **Help for `.simple`**');
    expect(detailedHelp).toContain('Simple command');
    expect(detailedHelp).not.toContain('Configuration');
  });
});
