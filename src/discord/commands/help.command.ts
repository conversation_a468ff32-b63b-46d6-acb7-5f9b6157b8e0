import { Injectable } from '@nestjs/common';
import { Message } from 'discord.js-selfbot-v13';
import { BaseCommand } from './base/base.command';
import { CommandExecutionResult } from '../types/command.types';
import { CommandRegistryService } from '../services/command-registry.service';

/**
 * Help command implementation
 * Displays available commands and their descriptions
 */
@Injectable()
export class HelpCommand extends BaseCommand {
  constructor(private readonly commandRegistry: CommandRegistryService) {
    super('help', {
      enabled: true,
      cooldown: 2000, // 2 second cooldown
    });
  }

  get name(): string {
    return 'help';
  }

  get description(): string {
    return 'Shows available commands and their descriptions';
  }

  get trigger(): string {
    return '.help';
  }

  /**
   * Execute the help command
   * @param message - The Discord message that triggered the command
   * @returns Promise with execution result including help information
   */
  protected async executeCommand(message: Message): Promise<CommandExecutionResult> {
    // Validate the message
    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format');
    }

    try {
      // Get all registered commands
      const commands = this.commandRegistry.getAllCommands();
      
      // Build help response
      const helpResponse = this.buildHelpResponse(commands);
      
      // Edit the original message with help information
      await message.edit(helpResponse);
      
      this.logger.debug(`Successfully displayed help for ${commands.length} commands`);
      
      return {
        success: true,
        response: helpResponse,
        metadata: {
          commandCount: commands.length,
          responseLength: helpResponse.length,
        },
      };
    } catch (error) {
      this.logger.error('Failed to generate help response:', error);
      throw new Error(`Failed to generate help: ${error.message}`);
    }
  }

  /**
   * Build the help response message
   * @param commands - Array of available commands
   * @returns Formatted help string
   */
  private buildHelpResponse(commands: any[]): string {
    if (commands.length === 0) {
      return '❌ No commands available';
    }

    // Sort commands alphabetically by name
    const sortedCommands = commands.sort((a, b) => a.name.localeCompare(b.name));

    // Build help sections
    const sections: string[] = [];
    
    // Header
    sections.push('📚 **Available Commands**');
    sections.push('');

    // Command list
    sortedCommands.forEach(command => {
      const trigger = command.trigger || `.${command.name}`;
      const description = command.description || 'No description available';
      sections.push(`• \`${trigger}\` - ${description}`);
    });

    // Footer
    sections.push('');
    sections.push('💡 **Usage**: Type any command to execute it');
    sections.push('⚠️ **Note**: Self-bot for educational purposes only');

    return sections.join('\n');
  }

  /**
   * Get command statistics for help display
   * @returns Command statistics object
   */
  private getCommandStats(): {
    total: number;
    enabled: number;
    categories: Record<string, number>;
  } {
    const commands = this.commandRegistry.getAllCommands();
    const stats = {
      total: commands.length,
      enabled: 0,
      categories: {} as Record<string, number>,
    };

    commands.forEach(command => {
      // Count enabled commands (if config is available)
      if (command.getConfig && command.getConfig().enabled !== false) {
        stats.enabled++;
      }

      // Categorize commands (basic categorization)
      const category = this.categorizeCommand(command.name);
      stats.categories[category] = (stats.categories[category] || 0) + 1;
    });

    return stats;
  }

  /**
   * Categorize a command based on its name
   * @param commandName - The command name
   * @returns Category string
   */
  private categorizeCommand(commandName: string): string {
    const utilityCommands = ['ping', 'help', 'status', 'info'];
    const funCommands = ['hurt', 'owo', 'meow', 'hentai'];
    
    if (utilityCommands.includes(commandName.toLowerCase())) {
      return 'Utility';
    } else if (funCommands.includes(commandName.toLowerCase())) {
      return 'Fun';
    } else {
      return 'Other';
    }
  }

  /**
   * Build detailed help for a specific command
   * @param commandName - Name of the command to get help for
   * @returns Detailed help string or null if command not found
   */
  buildDetailedHelp(commandName: string): string | null {
    const command = this.commandRegistry.getCommand(`.${commandName}`) || 
                   this.commandRegistry.getCommand(commandName);

    if (!command) {
      return null;
    }

    const sections: string[] = [];
    
    sections.push(`📖 **Help for \`${command.trigger}\`**`);
    sections.push('');
    sections.push(`**Description**: ${command.description || 'No description available'}`);
    sections.push(`**Usage**: \`${command.trigger}\``);
    
    // Add configuration info if available
    if (command.getConfig) {
      const config = command.getConfig();
      sections.push('');
      sections.push('**Configuration**:');
      sections.push(`• Enabled: ${config.enabled ? '✅' : '❌'}`);
      if (config.cooldown) {
        sections.push(`• Cooldown: ${config.cooldown}ms`);
      }
      if (config.aliases && config.aliases.length > 0) {
        sections.push(`• Aliases: ${config.aliases.join(', ')}`);
      }
    }

    return sections.join('\n');
  }
}
