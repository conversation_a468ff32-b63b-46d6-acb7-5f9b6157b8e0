import { Test, TestingModule } from '@nestjs/testing';
import { PingCommand } from './ping.command';
import { Message, Client } from 'discord.js-selfbot-v13';

describe('PingCommand', () => {
  let command: PingCommand;
  let mockClient: jest.Mocked<Client>;

  beforeEach(async () => {
    // Create mock client
    mockClient = {
      ws: {
        ping: 50,
      },
      readyAt: new Date(),
    } as jest.Mocked<Client>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: PingCommand,
          useFactory: () => new PingCommand(mockClient),
        },
      ],
    }).compile();

    command = module.get<PingCommand>(PingCommand);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
  });

  it('should have correct properties', () => {
    expect(command.name).toBe('ping');
    expect(command.description).toBe('Responds with pong and WebSocket latency');
    expect(command.trigger).toBe('.ping');
  });

  it('should edit message with latency', async () => {
    const mockMessage = {
      content: '.ping',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(mockMessage.edit).toHaveBeenCalledWith('pong 50ms');
    expect(result.success).toBe(true);
    expect(result.latency).toBe(50);
    expect(result.response).toBe('pong 50ms');
  });

  it('should handle unavailable latency', async () => {
    // Create a new command with unavailable ping
    const mockClientWithNoPing = {
      ws: {
        ping: -1,
      },
      readyAt: new Date(),
    } as jest.Mocked<Client>;

    const commandWithNoPing = new PingCommand(mockClientWithNoPing);

    const mockMessage = {
      content: '.ping',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await commandWithNoPing.execute(mockMessage);

    expect(mockMessage.edit).toHaveBeenCalledWith('pong (latency unavailable)');
    expect(result.success).toBe(true);
    expect(result.latency).toBe(-1);
  });

  it('should handle errors gracefully', async () => {
    const mockMessage = {
      content: '.ping',
      edit: jest.fn()
        .mockRejectedValueOnce(new Error('Test error'))
        .mockResolvedValueOnce(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Test error');
    expect(mockMessage.edit).toHaveBeenCalledTimes(2);
  });

  it('should validate message format', async () => {
    const mockMessage = {
      content: 'invalid',
      edit: jest.fn().mockResolvedValue(undefined),
    } as unknown as Message;

    const result = await command.execute(mockMessage);

    expect(result.success).toBe(false);
    expect(result.error).toBe('Invalid message format');
  });
});
