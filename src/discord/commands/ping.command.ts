import { Injectable } from '@nestjs/common';
import { Message, Client } from 'discord.js-selfbot-v13';
import { BaseCommand } from './base/base.command';
import { CommandExecutionResult } from '../types/command.types';

/**
 * Ping command implementation
 * Responds with pong and WebSocket latency information
 */
@Injectable()
export class PingCommand extends BaseCommand {
  constructor(private readonly client: Client) {
    super('ping', {
      enabled: true,
      cooldown: 1000, // 1 second cooldown
    });
  }

  get name(): string {
    return 'ping';
  }

  get description(): string {
    return 'Responds with pong and WebSocket latency';
  }

  get trigger(): string {
    return '.ping';
  }

  /**
   * Execute the ping command
   * @param message - The Discord message that triggered the command
   * @returns Promise with execution result including latency
   */
  protected async executeCommand(message: Message): Promise<CommandExecutionResult> {
    // Validate the message
    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format');
    }

    // Get WebSocket ping latency
    const wsLatency = this.client.ws.ping;

    // Handle case where ping is not available yet
    if (wsLatency === -1) {
      const response = 'pong (latency unavailable)';
      await message.edit(response);

      return {
        success: true,
        response,
        latency: -1,
        metadata: {
          reason: 'WebSocket ping not available yet',
        },
      };
    }

    // Format response with latency information
    const response = `pong ${wsLatency}ms`;

    // Edit the original message
    await message.edit(response);

    this.logger.debug(`Successfully responded with: ${response}`);

    return {
      success: true,
      response,
      latency: wsLatency,
      metadata: {
        wsLatency,
        clientReady: this.client.readyAt !== null,
      },
    };
  }
}
