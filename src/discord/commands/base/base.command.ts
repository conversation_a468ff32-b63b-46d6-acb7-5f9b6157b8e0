import { Logger } from '@nestjs/common';
import { Message } from 'discord.js-selfbot-v13';
import { ICommand } from '../../interfaces/command.interface';
import { CommandExecutionResult, CommandConfig } from '../../types/command.types';

/**
 * Abstract base class for all Discord commands
 * Implements common functionality and enforces command structure
 */
export abstract class BaseCommand implements ICommand {
  protected readonly logger: Logger;
  protected readonly config: CommandConfig;

  constructor(
    protected readonly commandName: string,
    config: Partial<CommandConfig> = {},
  ) {
    this.logger = new Logger(`${this.constructor.name}`);
    this.config = {
      enabled: true,
      cooldown: 0,
      permissions: [],
      aliases: [],
      ...config,
    };
  }

  /**
   * The name of the command
   */
  abstract get name(): string;

  /**
   * Description of what the command does
   */
  abstract get description(): string;

  /**
   * The trigger text that activates this command
   */
  abstract get trigger(): string;

  /**
   * Execute the command with error handling and logging
   * @param message - The Discord message that triggered the command
   * @returns Promise with execution result
   */
  async execute(message: Message): Promise<CommandExecutionResult> {
    const startTime = Date.now();

    try {
      // Check if command is enabled
      if (!this.config.enabled) {
        this.logger.warn(`Command ${this.name} is disabled`);
        return {
          success: false,
          error: 'Command is currently disabled',
          responseTime: Date.now() - startTime,
        };
      }

      this.logger.debug(`Executing command: ${this.name}`);

      // Execute the actual command logic
      const result = await this.executeCommand(message);

      // Add response time if not already set
      if (!result.responseTime) {
        result.responseTime = Date.now() - startTime;
      }

      this.logger.debug(
        `Command ${this.name} executed successfully in ${result.responseTime}ms`,
      );

      return result;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Failed to execute command ${this.name}:`, error);

      // Try to send error message to user
      await this.handleError(message, error);

      return {
        success: false,
        error: error.message,
        responseTime,
      };
    }
  }

  /**
   * Abstract method that subclasses must implement
   * Contains the actual command logic
   * @param message - The Discord message that triggered the command
   * @returns Promise with execution result
   */
  protected abstract executeCommand(message: Message): Promise<CommandExecutionResult>;

  /**
   * Handle errors by editing the message with an error response
   * @param message - The original message
   * @param error - The error that occurred
   */
  protected async handleError(message: Message, error: Error): Promise<void> {
    try {
      await message.edit(`❌ Error: ${error.message}`);
    } catch (editError) {
      this.logger.error('Failed to edit message with error:', editError);
    }
  }

  /**
   * Validate message content before processing
   * @param message - The Discord message
   * @returns True if message is valid
   */
  protected validateMessage(message: Message): boolean {
    if (!message || !message.content) {
      this.logger.warn('Invalid message received');
      return false;
    }

    if (!message.content.startsWith(this.trigger)) {
      this.logger.warn(`Message does not start with trigger: ${this.trigger}`);
      return false;
    }

    return true;
  }

  /**
   * Get command configuration
   * @returns Command configuration
   */
  getConfig(): CommandConfig {
    return { ...this.config };
  }

  /**
   * Update command configuration
   * @param config - Partial configuration to update
   */
  updateConfig(config: Partial<CommandConfig>): void {
    Object.assign(this.config, config);
    this.logger.debug(`Updated configuration for command ${this.name}`);
  }
}
