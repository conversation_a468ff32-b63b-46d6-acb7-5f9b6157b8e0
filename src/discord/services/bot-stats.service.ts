import { Injectable, Logger } from '@nestjs/common';
import { Client } from 'discord.js-selfbot-v13';
import { BotStats, ConnectionStatus } from '../types/command.types';

/**
 * Service for tracking and managing bot statistics
 */
@Injectable()
export class BotStatsService {
  private readonly logger = new Logger(BotStatsService.name);
  private startTime: Date;
  private commandsExecuted = 0;
  private messagesProcessed = 0;
  private lastActivity: Date;
  private currentStatus: ConnectionStatus = ConnectionStatus.DISCONNECTED;

  constructor(private readonly client: Client) {
    this.startTime = new Date();
    this.lastActivity = new Date();
    this.setupEventListeners();
  }

  /**
   * Setup event listeners to track bot activity
   */
  private setupEventListeners(): void {
    this.client.on('ready', () => {
      this.currentStatus = ConnectionStatus.CONNECTED;
      this.lastActivity = new Date();
      this.logger.debug('Bot status updated to CONNECTED');
    });

    this.client.on('disconnect', () => {
      this.currentStatus = ConnectionStatus.DISCONNECTED;
      this.lastActivity = new Date();
      this.logger.debug('Bot status updated to DISCONNECTED');
    });

    this.client.on('error', () => {
      this.currentStatus = ConnectionStatus.ERROR;
      this.lastActivity = new Date();
      this.logger.debug('Bot status updated to ERROR');
    });

    this.client.on('messageCreate', () => {
      this.messagesProcessed++;
      this.lastActivity = new Date();
    });
  }

  /**
   * Increment the command execution counter
   */
  incrementCommandsExecuted(): void {
    this.commandsExecuted++;
    this.lastActivity = new Date();
    this.logger.debug(`Commands executed: ${this.commandsExecuted}`);
  }

  /**
   * Get current bot statistics
   * @returns Current bot statistics
   */
  getStats(): BotStats {
    return {
      status: this.currentStatus,
      ping: this.client.ws.ping,
      uptime: Date.now() - this.startTime.getTime(),
      commandsExecuted: this.commandsExecuted,
      messagesProcessed: this.messagesProcessed,
      lastActivity: this.lastActivity,
    };
  }

  /**
   * Get formatted uptime string
   * @returns Human-readable uptime string
   */
  getFormattedUptime(): string {
    const uptime = Date.now() - this.startTime.getTime();
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  /**
   * Reset statistics
   */
  resetStats(): void {
    this.commandsExecuted = 0;
    this.messagesProcessed = 0;
    this.startTime = new Date();
    this.lastActivity = new Date();
    this.logger.debug('Bot statistics reset');
  }

  /**
   * Get connection status
   * @returns Current connection status
   */
  getConnectionStatus(): ConnectionStatus {
    return this.currentStatus;
  }

  /**
   * Check if bot is healthy
   * @returns True if bot is in good state
   */
  isHealthy(): boolean {
    const stats = this.getStats();
    const timeSinceLastActivity = Date.now() - stats.lastActivity.getTime();
    
    // Consider unhealthy if no activity for more than 5 minutes
    const isRecentlyActive = timeSinceLastActivity < 5 * 60 * 1000;
    
    return (
      stats.status === ConnectionStatus.CONNECTED &&
      stats.ping > -1 &&
      isRecentlyActive
    );
  }

  /**
   * Get health check information
   * @returns Health check details
   */
  getHealthCheck(): {
    healthy: boolean;
    status: ConnectionStatus;
    ping: number;
    uptime: number;
    lastActivity: Date;
    issues: string[];
  } {
    const stats = this.getStats();
    const issues: string[] = [];
    
    if (stats.status !== ConnectionStatus.CONNECTED) {
      issues.push(`Connection status is ${stats.status}`);
    }
    
    if (stats.ping === -1) {
      issues.push('WebSocket ping unavailable');
    }
    
    const timeSinceLastActivity = Date.now() - stats.lastActivity.getTime();
    if (timeSinceLastActivity > 5 * 60 * 1000) {
      issues.push('No recent activity detected');
    }

    return {
      healthy: this.isHealthy(),
      status: stats.status,
      ping: stats.ping,
      uptime: stats.uptime,
      lastActivity: stats.lastActivity,
      issues,
    };
  }
}
