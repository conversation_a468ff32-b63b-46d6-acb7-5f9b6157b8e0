import { Injectable, Logger } from '@nestjs/common';
import { ICommand, ICommandRegistry } from '../interfaces/command.interface';

/**
 * Service for managing command registration and retrieval
 * Implements the Command Registry pattern
 */
@Injectable()
export class CommandRegistryService implements ICommandRegistry {
  private readonly logger = new Logger(CommandRegistryService.name);
  private readonly commands = new Map<string, ICommand>();

  /**
   * Register a command in the registry
   * @param command - The command to register
   */
  register(command: ICommand): void {
    if (!command) {
      throw new Error('Command cannot be null or undefined');
    }

    if (!command.trigger) {
      throw new Error(`Command ${command.name} must have a trigger`);
    }

    if (this.commands.has(command.trigger)) {
      this.logger.warn(
        `Command with trigger '${command.trigger}' already exists. Overwriting...`,
      );
    }

    this.commands.set(command.trigger, command);
    this.logger.debug(
      `Registered command: ${command.name} with trigger '${command.trigger}'`,
    );
  }

  /**
   * Get a command by its trigger
   * @param trigger - The trigger text
   * @returns The command if found, undefined otherwise
   */
  getCommand(trigger: string): ICommand | undefined {
    if (!trigger) {
      return undefined;
    }

    return this.commands.get(trigger);
  }

  /**
   * Get all registered commands
   * @returns Array of all commands
   */
  getAllCommands(): ICommand[] {
    return Array.from(this.commands.values());
  }

  /**
   * Check if a trigger is registered
   * @param trigger - The trigger text to check
   * @returns True if trigger is registered
   */
  hasCommand(trigger: string): boolean {
    return this.commands.has(trigger);
  }

  /**
   * Unregister a command
   * @param trigger - The trigger of the command to unregister
   * @returns True if command was removed, false if not found
   */
  unregister(trigger: string): boolean {
    const removed = this.commands.delete(trigger);
    if (removed) {
      this.logger.debug(`Unregistered command with trigger: ${trigger}`);
    } else {
      this.logger.warn(`Attempted to unregister non-existent command: ${trigger}`);
    }
    return removed;
  }

  /**
   * Clear all registered commands
   */
  clear(): void {
    const count = this.commands.size;
    this.commands.clear();
    this.logger.debug(`Cleared ${count} commands from registry`);
  }

  /**
   * Get command statistics
   * @returns Object with registry statistics
   */
  getStats(): {
    totalCommands: number;
    triggers: string[];
    commandNames: string[];
  } {
    const commands = this.getAllCommands();
    return {
      totalCommands: commands.length,
      triggers: commands.map(cmd => cmd.trigger),
      commandNames: commands.map(cmd => cmd.name),
    };
  }

  /**
   * Find commands that match a partial trigger
   * @param partialTrigger - Partial trigger text
   * @returns Array of matching commands
   */
  findCommands(partialTrigger: string): ICommand[] {
    if (!partialTrigger) {
      return [];
    }

    return this.getAllCommands().filter(cmd =>
      cmd.trigger.toLowerCase().includes(partialTrigger.toLowerCase()),
    );
  }

  /**
   * Validate all registered commands
   * @returns Array of validation errors
   */
  validateCommands(): string[] {
    const errors: string[] = [];
    
    for (const command of this.getAllCommands()) {
      if (!command.name) {
        errors.push(`Command with trigger '${command.trigger}' has no name`);
      }
      
      if (!command.description) {
        errors.push(`Command '${command.name}' has no description`);
      }
      
      if (!command.execute || typeof command.execute !== 'function') {
        errors.push(`Command '${command.name}' has no execute method`);
      }
    }

    if (errors.length > 0) {
      this.logger.warn(`Found ${errors.length} command validation errors`);
    }

    return errors;
  }
}
