import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppConfigService {
  constructor(private configService: ConfigService) {}

  get discordToken(): string {
    const token = this.configService.get<string>('DISCORD_TOKEN');
    if (!token) {
      throw new Error(
        'DISCORD_TOKEN is not defined in environment variables. Please check your .env file.',
      );
    }
    return token;
  }

  get nodeEnv(): string {
    return this.configService.get<string>('NODE_ENV', 'development');
  }

  get port(): number {
    return this.configService.get<number>('PORT', 3000);
  }

  get isDevelopment(): boolean {
    return this.nodeEnv === 'development';
  }

  get isProduction(): boolean {
    return this.nodeEnv === 'production';
  }
}
