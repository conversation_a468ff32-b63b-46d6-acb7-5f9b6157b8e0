import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    const app = await NestFactory.create(AppModule);
    
    // Enable graceful shutdown
    app.enableShutdownHooks();
    
    const port = process.env.PORT || 3000;
    await app.listen(port);
    
    logger.log(`🚀 Discord Self-Bot is running on port ${port}`);
    logger.warn('⚠️  WARNING: Self-bots violate Discord\'s Terms of Service');
    logger.warn('⚠️  This is for educational purposes only!');
  } catch (error) {
    logger.error('❌ Failed to start the application', error);
    process.exit(1);
  }
}

bootstrap();
